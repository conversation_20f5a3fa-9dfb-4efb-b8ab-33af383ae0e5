{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "allowJs": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "paths": {"@p2p/core": ["./packages/p2p-core/src"], "@p2p/types": ["./packages/types/src"], "@p2p/ui": ["./packages/ui-components/src"]}}, "include": ["apps/*/src/**/*", "packages/*/src/**/*"], "exclude": ["node_modules", "dist", "build"]}