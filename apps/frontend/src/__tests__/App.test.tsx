import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import App from '../App';

// Mock the P2P service
jest.mock('../services/P2PService', () => ({
  p2pService: {
    initialize: jest.fn(),
    joinRoom: jest.fn(),
    sendMessage: jest.fn(),
    broadcastMessage: jest.fn(),
    initiateFileTransfer: jest.fn(),
    acceptFileTransfer: jest.fn(),
    rejectFileTransfer: jest.fn(),
    getConnectionStatus: jest.fn(() => false),
    disconnect: jest.fn(),
  },
}));

// Mock Zustand store
jest.mock('../stores/useP2PStore', () => ({
  useP2PStore: jest.fn(() => ({
    notifications: [],
    removeNotification: jest.fn(),
    messages: [],
    currentUserId: null,
    isConnected: false,
    fileTransfers: [],
    peers: new Map(),
    connectionStates: new Map(),
  })),
  selectConnectedPeers: jest.fn(() => []),
}));

describe('App Component', () => {
  test('renders room join interface initially', () => {
    render(<App />);
    
    expect(screen.getByText('Join P2P Chat')).toBeInTheDocument();
    expect(screen.getByText('Enter a room ID to start chatting securely with others')).toBeInTheDocument();
    expect(screen.getByLabelText('Your Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Room ID')).toBeInTheDocument();
  });

  test('shows join room button', () => {
    render(<App />);
    
    const joinButton = screen.getByRole('button', { name: /join room/i });
    expect(joinButton).toBeInTheDocument();
    expect(joinButton).toBeDisabled(); // Should be disabled when fields are empty
  });

  test('shows generate room ID button', () => {
    render(<App />);
    
    const generateButton = screen.getByRole('button', { name: /generate random room id/i });
    expect(generateButton).toBeInTheDocument();
  });

  test('displays privacy features', () => {
    render(<App />);
    
    expect(screen.getByText('All messages are sent directly between peers')).toBeInTheDocument();
    expect(screen.getByText('No data is stored on servers')).toBeInTheDocument();
    expect(screen.getByText('Connections are encrypted by default')).toBeInTheDocument();
  });
});
